{"name": "shrimp-task-viewer", "version": "2.1.0", "description": "Web-based viewer for Shrimp Task Manager data with comprehensive testing suite (port 9998)", "type": "module", "main": "server.js", "bin": {"shrimp-viewer": "cli.js"}, "scripts": {"dev": "vite build --watch", "build": "vite build", "preview": "vite preview", "start": "node server.js", "stop": "node cli.js stop", "restart": "node cli.js restart", "status": "node cli.js status", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "keywords": ["shrimp", "task-manager", "viewer", "web", "dashboard", "mcp", "auto-discovery"], "author": "SoraOrc Team", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/cjo4m06/mcp-shrimp-task-manager.git"}, "bugs": {"url": "https://github.com/cjo4m06/mcp-shrimp-task-manager/issues"}, "files": ["server.js", "cli.js", "README.md", "example-config.json"], "homepage": "https://github.com/cjo4m06/mcp-shrimp-task-manager#readme", "dependencies": {"@tanstack/react-table": "^8.21.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.6.0", "busboy": "^1.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-syntax-highlighter": "^15.6.1", "vite": "^5.4.19"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.1", "@vitest/ui": "^2.1.8", "jsdom": "^25.0.1", "supertest": "^7.0.0", "vitest": "^2.1.8"}}