# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - dataDir
    properties:
      dataDir:
        type: string
        description: Absolute path to the data directory for task data storage
    description: Configuration for shrimp task manager. dataDir must be an absolute path.
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => ({
        command: 'node',
        args: ['/mcp-shrimp-task-manager/dist/index.js'],
        env: { DATA_DIR: config.dataDir }
    })
  exampleConfig:
    dataDir: /var/lib/mcp-shrimp-task-manager/data
