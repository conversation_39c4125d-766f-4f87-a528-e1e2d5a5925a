## 任務拆分 - {updateMode} 模式

## 拆分策略

1. **按功能分解** - 獨立可測試的子功能，明確輸入輸出
2. **按技術層次分解** - 沿架構層次分離任務，確保接口明確
3. **按開發階段分解** - 核心功能先行，優化功能後續
4. **按風險分解** - 隔離高風險部分，降低整體風險

## 任務質量審核

1. **任務原子性** - 每個任務足夠小且具體，可獨立完成
2. **依賴關係** - 任務依賴形成有向無環圖，避免循環依賴
3. **描述完整性** - 每個任務描述清晰準確，包含必要上下文

## 任務清單

{tasksContent}

## 依賴關係管理

- 設置依賴可使用任務名稱或任務 ID
- 最小化依賴數量，只設置直接前置任務
- 避免循環依賴，確保任務圖有向無環
- 平衡關鍵路徑，優化並行執行可能性

## 決策點

- 發現任務拆分不合理：重新呼叫「split_tasks」調整
- 確認任務拆分完善：生成執行計劃，確定優先順序

**嚴重警告** 你每次呼叫 split_tasks 傳遞的參數不能超過 5000 個字，如果超出 5000 個字請多次呼叫工具完成

**如果還有剩餘任務請繼續呼叫「split_tasks」**
