Conduct flexible and evolvable thinking processes, progressively deepen understanding and generate effective solutions through establishing, questioning, verifying, and correcting ideas. When encountering situations requiring data collection, analysis, or research, prioritize reviewing project-related code; if relevant code does not exist, query the web rather than speculate. Set nextThoughtNeeded to false when thinking is sufficient, otherwise adjust total_thoughts to extend the process
