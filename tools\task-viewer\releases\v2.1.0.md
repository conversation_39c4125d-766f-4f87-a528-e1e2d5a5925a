# 🚀 Task Viewer v2.1.0 Release Notes

*Released: July 29, 2025*

## 🎉 What's New

### 🔗 Clickable File Paths with Project Root Support
**Copy full file paths with one click!**

- **Click-to-Copy File Paths**: Now when you click on a task, and are taken to the Task Details page, if there are any related files listed that the task modifies or creates, that filename will now have a hyperlink to the actual file on your filesystem (provided that you configure the project folder when creating / editing the profile tab)

### 📋 Enhanced UUID Management
**Streamlined UUID copying with intuitive interactions**

When interfacing with claude, sometimes it is useful to easily reference a shrimp task, for example:
"<PERSON>, please complete this shrimp task: da987923-2afe-4ac3-985e-ac029cc831e7".  Therefore, we added a Click-to-copy feature on Task # badges, and on the UUID listed in the Task Name column.

- **Click-to-Copy Task Badges**: Click any task number badge to instantly copy its UUID
- **Concatinated UUID displayed under task name in Task Name Column**: Click UUID to copy

### 🔄 Task Dependencies Column for easy Paralization 

We added a Dependencies column that lists the linked UUID's of any dependent tasks.  Now you can easily navigate to dependent tasks.

### 🤖 AI Instruction Actions
**One-click AI task instructions**

We added an Actions Column that has a handy Robot emoji. If you click the emoji, it will copy an AI Instruction to the clipboard which you can then paste into your agent's chat.  The instruction has been coded to copy the following: "Use task manager to complete this shrimp task: < UUID >" 

This instruction is useful for paralization. For example, if the following 3 tasks don't have dependencies,you can open up several terminal windows, and paste in the AI Instructions.  Example:

Terminal 1: Use task manager to complete this shrimp task: da987923-2afe-4ac3-985e-ac029cc831e7
Terminal 2: Use task manager to complete this shrimp task: 4afe3f1c-bf7f-4642-8485-668c33a1e0fc
Terminal 3: Use task manager to complete this shrimp task: 21bd2cb9-**************-885ee2b0792e

### ✏️ Profile Edit Button

**Project Root Configuration**: Now you can set project root per profile, this will then allow you to enable full file path copying when "related files" when viewing task the details page.

**Ability to Rename a Profile**: Now you can rename a profile tab without having to delete and recreate.



## 🔄 Changes

### UI/UX Improvements
- **Streamlined Copy Actions**: Consolidated UUID copying to task badge click only
- **Dependencies Over Notes**: Replaced Notes column with more useful Dependencies column
- **In-App Release Notes**: Release notes for Task viewer displayed in the top banner
- **Tab-Based Navigation**: Release notes integrated into the tab system with close functionality

### Architecture Updates
- **ES Module Compatibility**: Removed busboy dependency for better ES module support
- **Native Form Parsing**: Replaced third-party form parsing with Node.js built-ins
- **Version Bump**: Updated to v2.1.0 (for the task veiwer) to reflect significant feature additions

## 🐛 Bug Fixes

### 🚨 CRITICAL FIX: File Upload Creates Static Copies
**The Problem**: When adding profiles by uploading a tasks.json file, the system was creating a static copy in `/tmp/` directory. This meant that any changes to your actual task file would NOT be reflected in the viewer - tasks would appear stuck in their original state (e.g., showing "in progress" when actually "completed").

**The Solution**: Removed file upload entirely. Now you must enter the folder path directly, and the system automatically appends `/tasks.json`. This ensures the viewer always reads from your actual live file.

**How to use**: 
1. Navigate to your shrimp data folder in terminal
2. Type `pwd` to get the full path (highlighted in yellow in the UI)
3. Paste this path into the "Task Folder Path" field
4. The system automatically uses `[your-path]/tasks.json`

![Add Profile Modal Screenshot](/releases/add-profile-modal.png)

### Profile Management
- **Auto-Selection Fixed**: New profiles are now automatically selected and loaded after creation
- **Import Issues Resolved**: Fixed ES module import problems with busboy library
- **Unified Edit Modal**: Combined rename and project root editing into single interface

### Data Handling
- **Project Root Persistence**: Project root paths now properly saved with profile data
- **Task Loading**: Fixed race conditions when switching between profiles
- **State Management**: Improved handling of profile selection state

## 🗑️ Removed

### Deprecated Features
- **Busboy Dependency**: Replaced with native Node.js form parsing
- **Notes Column**: Replaced by more useful Dependencies column
- **Individual Copy Buttons**: UUID copying consolidated to task badge click
- **Separate Rename Button**: Merged into unified Edit Profile button

## 📝 Technical Details

### New API Endpoints
- **PUT /api/update-profile/:id**: Update profile name and settings
- **Enhanced /api/tasks/:id**: Now includes projectRoot in response
- **GET /releases/*.md**: Serve release notes markdown files

### Frontend Components
- **ReleaseNotes Component**: Beautiful markdown-rendered release notes
- **Enhanced TaskTable**: Support for dependencies and actions columns
- **Improved TaskDetailView**: Clickable file paths with full path copying

### Configuration
- **Project Root Storage**: Profiles now store optional projectRoot path
- **Settings Persistence**: All profile data saved to ~/.shrimp-task-viewer-settings.json

## 🎯 Summary

Version 2.1.0 transforms the Task Viewer into a more integrated development tool with enhanced file path management, improved UUID handling, and better task relationship visualization. The unified profile management and in-app release notes provide a more cohesive user experience while maintaining the clean, intuitive interface.