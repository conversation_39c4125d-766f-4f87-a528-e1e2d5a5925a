{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Shrimp Task Viewer Configuration", "description": "Configuration file for the Shrimp Task Manager Viewer", "type": "object", "properties": {"agents": {"type": "array", "description": "List of task data sources to display", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for this data source"}, "name": {"type": "string", "description": "Display name for this data source"}, "path": {"type": "string", "description": "Absolute path to the tasks.json file"}}, "required": ["id", "name", "path"]}}}, "required": ["agents"], "examples": [{"agents": [{"id": "team1-neo", "name": "Team 1 - <PERSON> (Development)", "path": "/home/<USER>/claude/SoraOrc/teams/team-1/dev__neo/shrimp_data/tasks.json"}, {"id": "team1-trinity", "name": "Team 1 - <PERSON> (Testing)", "path": "/home/<USER>/claude/SoraOrc/teams/team-1/testing__trinity/shrimp_data/tasks.json"}, {"id": "team2-morpheus", "name": "Team 2 - <PERSON><PERSON><PERSON> (Development)", "path": "/home/<USER>/claude/SoraOrc/teams/team-2/dev__morpheus/shrimp_data/tasks.json"}, {"id": "team2-cipher", "name": "Team 2 - <PERSON><PERSON><PERSON> (Testing)", "path": "/home/<USER>/claude/SoraOrc/teams/team-2/testing__cipher/shrimp_data_cipher/tasks.json"}, {"id": "team3-homer", "name": "Team 3 - <PERSON> (Development)", "path": "/home/<USER>/claude/SoraOrc/teams/team-3/dev__homer/shrimp_data/tasks.json"}, {"id": "team3-bart", "name": "Team 3 - <PERSON> (Testing)", "path": "/home/<USER>/claude/SoraOrc/teams/team-3/testing__bart/shrimp_data_bart/tasks.json"}]}, {"agents": [{"id": "project-main", "name": "Main Project Tasks", "path": "/path/to/project/shrimp_data/tasks.json"}, {"id": "project-dev", "name": "Development Environment", "path": "/path/to/project/dev/tasks.json"}]}]}