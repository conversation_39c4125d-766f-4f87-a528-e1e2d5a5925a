# Development Guidelines

This document provides the dedicated specifications for AI Agents performing development tasks in the `mcp-shrimp-task-manager` project.

## 1. Project Overview

- **Project Name**: `mcp-shrimp-task-manager`
- **Purpose**: A task management tool designed for AI Agents, emphasizing chain-of-thought, reflection, and style consistency. It converts natural language into structured development tasks with dependency tracking and iterative refinement capabilities.
- **Technology Stack**:
  - **Primary Language**: TypeScript
  - **Runtime Environment**: Node.js (ES Module)
  - **Main Frameworks/Libraries**: Express.js (for potential API or WebGUI), Zod (for data validation)
  - **Package Manager**: npm
- **Core Features**:
  - Natural language task parsing
  - Structured task generation and management
  - Task dependency tracking
  - Task execution and verification assistance
  - Integration with AI Agent's thought processes

## 2. Project Architecture

- **Main Source Code Directory**: `src/`
  - `src/index.ts`: Main application entry point or module export point. **Modifications to this file require careful impact assessment.**
  - `src/utils/`: General-purpose utility functions.
  - `src/types/`: TypeScript type definitions. **When adding or modifying types, ensure consistency with Zod schemas (if applicable).**
  - `src/tools/`: Project-specific tools or modules for integration with external services.
  - `src/models/`: Data model definitions (potentially related to Zod schemas).
  - `src/prompts/`: Prompt templates related to AI interaction. **Modifying or adding prompts requires consideration of the potential impact on AI Agent behavior.**
  - `src/public/`: WebGUI or other static assets.
  - `src/tests/`: Unit and integration tests.
- **Compiled Output Directory**: `dist/` (This directory is auto-generated by `tsc`. **Do not manually modify its contents.**).
- **Configuration Files**:
  - `package.json`: Project dependencies and scripts. **After adding dependencies, you must run `npm install`.**
  - `tsconfig.json`: TypeScript compilation settings. **Do not modify the `"strict": true` setting unless absolutely necessary.**
  - `.env.example` & `.env`: Environment variable settings. **Sensitive information must not be committed to version control.**
- **Documentation**:
  - `README.md`: Main project documentation.
  - `docs/`: May contain more detailed architecture, API documentation, etc.
  - `CHANGELOG.md`: Record of version changes. **Must be updated before releasing a new version.**
  - `data/WebGUI.md`: Contains the link to the Task Manager UI.

## 3. Code Specifications

### 3.1. Naming Conventions

- **Variables and Functions**: Use camelCase.
  - _Do_: `const taskName = "example"; function processTask() {}`
  - _Don't_: `const Task_Name = "example"; function Process_Task() {}`
- **Classes and Interfaces**: Use PascalCase.
  - _Do_: `class TaskManager {}; interface ITaskOptions {}`
  - _Don't_: `class taskManager {}; interface iTaskOptions {}`
- **File Names**: Use camelCase or kebab-case for `.ts` files.
  - _Do_: `taskProcessor.ts`, `task-utils.ts`
  - _Don't_: `TaskProcessor.ts`, `task_utils.ts`
- **Constants**: Use UPPER_SNAKE_CASE.
  - _Do_: `const MAX_RETRIES = 3;`
  - _Don't_: `const maxRetries = 3;`

### 3.2. Formatting Requirements

- **Indentation**: Use 2 spaces for indentation. **Do not use Tab characters.**
- **Semicolons**: Statements must end with a semicolon.
- **Quotes**: Prefer single quotes (`'`) for strings, unless the string itself contains single quotes.
  - _Do_: `const message = 'Hello World'; const complex = "It\\'s complex";`
  - _Don't_: `const message = "Hello World";`
- **Max Line Length**: Recommended not to exceed 120 characters.
- **Comments**:
  - Use `//` for single-line comments.
  - Use `/* ... */` for multi-line comments.
  - Use JSDoc-style comments for public functions, classes, and methods.
    - _Do_:
      ```typescript
      /**
       * Processes a given task.
       * @param taskId The ID of the task to process.
       * @returns True if successful, false otherwise.
       */
      function processTaskById(taskId: string): boolean {
        // implementation
        return true;
      }
      ```

### 3.3. TypeScript Specific Guidelines

- **Type Annotations**: All function parameters, return values, and variable declarations should have explicit type annotations. **The use of the `any` type is forbidden, except in very specific and unavoidable circumstances, which require a comment explaining the reason.**
  - _Do_: `function greet(name: string): string { return \`Hello, ${name}\`; }`
  - _Don't_: `function greet(name): any { return "Hello, " + name; }`
- **Interfaces vs. Type Aliases**: Prefer Interfaces for defining object shapes and Type Aliases for defining union types, tuples, or other complex types.
- **ES Modules**: Use `import` and `export` syntax.
  - _Do_: `import { Task } from './models/task'; export class TaskService {}`
  - _Don't_: `const Task = require('./models/task'); module.exports = TaskService;`
- **Strict Mode**: The project has `"strict": true` enabled. All TypeScript strict mode errors must be resolved.

## 4. Feature Implementation Guidelines

### 4.1. General Principles

- **Single Responsibility Principle (SRP)**: Each function and class should be responsible for only one piece of functionality.
- **Keep It Simple, Stupid (KISS)**: Avoid overly complex solutions.
- **Don't Repeat Yourself (DRY)**: Extract common logic into reusable functions or classes, stored in `src/utils/` or relevant modules.
- **Error Handling**:
  - Use `try...catch` to handle expected errors.
  - Provide clear error messages for critical operations.
  - Consider using custom error classes to provide richer error information.
- **Logging**:
  - Add logging for critical operations, error handling, and important state changes.
  - Consider using structured logging.
  - **Do not log sensitive information (e.g., passwords, API Keys).**

### 4.2. Zod Usage

- Data structures defined in `src/models/` or `src/types/` should primarily use Zod schemas for definition and validation.
- Zod schemas should be kept in sync with TypeScript types. You can use `z.infer<typeof schema>` to generate types.

  - _Do_:

    ```typescript
    import { z } from "zod";

    export const TaskSchema = z.object({
      id: z.string().uuid(),
      name: z.string().min(1),
      description: z.string().optional(),
    });

    export type Task = z.infer<typeof TaskSchema>;
    ```

### 4.3. Express.js Usage (if API/WebGUI exists)

- Route definitions should be clear and follow RESTful principles (for APIs).
- Middleware should be effectively organized, e.g., error handling middleware, logging middleware.
- All external input (request parameters, body, query) **must** be validated using Zod or a similar mechanism.

## 5. Framework/Plugin/Third-Party Library Usage Guidelines

- **Adding Dependencies**:
  - **Must** first evaluate the necessity, maintenance status, and security of the dependency.
  - Use `npm install <package-name>` (for runtime dependencies) or `npm install --save-dev <package-name>` (for development dependencies).
  - **Must** specify a clear version range in `package.json` (e.g., `^1.2.3` or `~1.2.3`), avoid using `*`.
- **Updating Dependencies**: Regularly check and update dependencies to the latest stable version to get security patches and new features. Assess potential breaking changes before updating.
- **Removing Dependencies**: If a dependency is no longer needed, remove it using `npm uninstall <package-name>` and remove related references from the code.

## 6. Workflow Guidelines

### 6.1. Development Process

1.  **Understand the Task**: Carefully read the task description, requirements, and acceptance criteria.
2.  **Branch Management**: Create a new feature branch from the latest `main` (or `develop`) branch. Branch names should be concise and clear, e.g., `feature/add-task-editing` or `fix/login-bug`.
3.  **Coding and Testing**:
    - Code according to these guidelines.
    - **Must** write unit tests (stored in `src/tests/`) for new features or bug fixes.
    - Run `npm run build` to ensure the code compiles successfully.
    - Run `npm run dev` or `npm run start` locally for testing.
4.  **Code Committing**:
    - Git commit messages should follow the Conventional Commits specification (e.g., `feat: add user authentication`, `fix: resolve issue with task sorting`).
    - **Do not commit code with `console.log` or other debugging messages to the main branches.**
5.  **Pull Request (PR)**:
    - Push the feature branch to the remote repository and create a Pull Request to the `main` (or `develop`) branch.
    - The PR description should clearly explain the changes and their purpose.
6.  **Code Review**: Wait for other developers or AI Agents to conduct a code review.
7.  **Merge and Deploy**: After the code review is passed, merge the PR. The deployment process follows the project setup.

### 6.2. Version Control (Git)

- **Main Branches**:
  - `main`: Represents the stable and deployable product version.
  - `develop` (if used): Represents the latest in-development version.
- **Commit Frequency**: Small, frequent, and meaningful commits are recommended.
- **Resolving Conflicts**: When merging or rebasing branches, if conflicts occur, they **must** be resolved carefully to ensure the correctness and integrity of the code.

### 6.3. CHANGELOG Updates

- Before releasing a new version, `CHANGELOG.md` **must** be updated.
- The log should include the version number, release date, and a list of new features, bug fixes, and breaking changes.

## 7. Key File Interaction Guidelines

- **Modifying `src/types/` or `src/models/` (especially Zod schemas)**:
  - **Must** check and update all files that reference these types or schemas to ensure type consistency.
  - **Must** re-run relevant tests.
- **Modifying `src/index.ts`**:
  - If the module's exported API is changed, **must** check all dependent projects or files and make necessary adjustments.
- **Modifying `package.json` (especially `dependencies` or `scripts`)**:
  - **Must** notify team members or relevant AI Agents to run `npm install`.
  - If `scripts` are modified, ensure the CI/CD process (if any) is updated accordingly.
- **Modifying `.env.example`**:
  - **Must** synchronously update `.env` files in all development environments and notify team members.
- **Modifying `README.md` or documents in `docs/`**:
  - If changes involve core features or usage, **must** ensure the accuracy and timeliness of the documentation.

## 8. AI Decision-Making Guidelines

### 8.1. Handling Vague Requests

- When receiving a vague development instruction (e.g., "optimize the task list display"):
  1.  **Attempt to Clarify**: If possible, request more specific details or expected outcomes from the user or task initiator.
  2.  **Analyze Context**: Check related code (`src/`), existing UI (if any), and relevant issues (if any) to infer intent.
  3.  **Propose Solutions**: Based on the analysis, propose 1-2 concrete implementation plans, explaining their pros, cons, and estimated effort.
  4.  **Wait for Confirmation**: Do not proceed with large-scale code modifications until clear instructions are received.

### 8.2. Error/Exception Handling Strategy

- **Priorities**:
  1.  **User Experience**: Avoid program crashes and provide friendly error messages.
  2.  **Data Integrity**: Ensure errors do not lead to data corruption or inconsistency.
  3.  **System Stability**: Log detailed error information for troubleshooting.
- **Choices**:
  - For predictable errors (e.g., invalid user input), handle them within the operation's context and provide feedback.
  - For unexpected system errors, catch, log, and potentially re-throw or trigger a global error handling mechanism.

### 8.3. Dependency Selection

- When needing to introduce a new third-party library:
  1.  **Check Existing**: Verify if a similar library that meets the requirements already exists in the project.
  2.  **Evaluate Options**:
      - **Activity and Community Support**: Choose well-maintained libraries with active communities.
      - **Lightweight**: Avoid introducing libraries that are too large or have redundant features.
      - **Security**: Check for known security vulnerabilities.
      - **License**: Ensure compatibility with the project's license.
  3.  **Minimization Principle**: Only introduce libraries that are genuinely needed.

## 9. Prohibitions

- **Do not directly modify any files in the `dist/` directory.** This directory contains compiled output.
- **Do not assume a new dependency is available without running `npm install`.**
- **Do not commit untested or unfinished code directly to the main branches (`main` or `develop`).** Use feature branches.
- **Do not commit code containing API Keys, passwords, or other sensitive information to the version control system.** Use the `.env` file for such information.
- **Do not make major changes to the core architecture or public API without notification and approval.**
- **Do not ignore TypeScript type errors.** All errors reported by `tsc` must be resolved.
- **Do not use the `any` type without a very good reason and a corresponding comment.**
- **Do not leave large amounts of `console.log` or other temporary debugging code in the codebase.**
- **Do not release a new version without updating `CHANGELOG.md`.**
- **Do not introduce third-party libraries incompatible with the project's MIT license.**

## 10. Updating This Guideline Document (`shrimp-rules.md`)

- When the project's tech stack, core architecture, main workflow, or important guidelines change, this document **must** be updated accordingly.
- Update requests should clearly specify the sections and content to be changed.
- If a vague "update rules" instruction is received, the AI Agent **must**:
  1.  Autonomously analyze the current codebase for changes (e.g., `git diff`, recent commits).
  2.  Compare the existing `shrimp-rules.md` with the project's current state to find inconsistent or outdated rules.
  3.  List the inferred update points and their justifications in the `process_thought` stage.
  4.  Propose specific modifications or directly edit this document.
  5.  **Strictly prohibited** to seek clarification from the user on a vague request before performing an autonomous analysis.

---

This development guide aims to ensure that AI Agents can participate in the development of the `mcp-shrimp-task-manager` project efficiently, consistently, and securely. 
