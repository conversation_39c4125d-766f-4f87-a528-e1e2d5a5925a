* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: #0a0e27;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  background: linear-gradient(135deg, #4fbdba, #7b68ee);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2.5rem;
  margin-bottom: 5px;
}

.version-info {
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.version-info a {
  color: #4fbdba;
  text-decoration: none;
  transition: color 0.3s;
}

.version-info a:hover {
  color: #7b68ee;
}

.controls {
  background: transparent;
  padding: 20px 20px 0 20px;
  margin-bottom: 0;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: flex-end;
  position: relative;
}

.tab-border-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #2c3e50;
  z-index: 0;
}

.profile-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.profile-tabs {
  flex: 1;
}

.tabs-list {
  display: flex;
  align-items: center;
  gap: 2px;
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #34495e;
  border: 2px solid #2c3e50;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  color: #bbb;
  cursor: move;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 0.9rem;
  user-select: none;
  position: relative;
  margin-bottom: -2px;
}

.tab:hover {
  background: #4a5f7a;
  color: #fff;
}

.tab.active {
  background: #16213e;
  color: #fff;
  font-weight: 600;
  border-color: #2c3e50;
  z-index: 1;
}

.tab.dragging {
  opacity: 0.5;
  transform: scale(0.95);
}

.tab.drag-over {
  border-left: 3px solid #4fbdba;
}

.tab-name {
  pointer-events: none;
}

.tab-name-input {
  background: white;
  border: 1px solid #0066cc;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 14px;
  max-width: 150px;
  outline: none;
  color: #333;
}

.tab-close-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0;
  margin-left: 4px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.tab-close-btn:hover {
  opacity: 1;
  color: #ff6b6b;
}

.add-tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #6494D3;
  border: 2px solid #2c3e50;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 0.9rem;
  user-select: none;
  position: relative;
  margin-bottom: -2px;
  font-weight: 600;
}

.add-tab-btn:hover {
  background: #5081c7;
  color: #fff;
}

.content-container {
  background: #16213e;
  border-radius: 0 0 10px 10px;
  border: 2px solid #2c3e50;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.stats-and-search-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  background: #16213e;
  padding: 15px 20px;
  border-bottom: 1px solid #2c3e50;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 15px;
}


.auto-refresh-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  white-space: nowrap;
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #aaa;
  white-space: nowrap;
}

.auto-refresh input[type="checkbox"] {
  transform: scale(1.2);
}

.refresh-interval-select {
  padding: 6px 8px;
  background: #2c3e50;
  border: 1px solid #34495e;
  border-radius: 4px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-interval-select:focus {
  outline: none;
  border-color: #4fbdba;
}

.refresh-interval-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #1a252f;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 300px;
  padding: 8px 12px;
  background: #1a252f;
  border: 2px solid #2c3e50;
  border-radius: 6px;
  color: white;
  font-size: 0.9rem;
  transition: border-color 0.3s;
}

.refresh-button {
  padding: 8px 12px;
  background: #2c3e50;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.refresh-button:hover:not(:disabled) {
  background: #4fbdba;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.rename-button {
  padding: 8px 12px;
  background: #6a5acd;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.rename-button:hover:not(:disabled) {
  background: #7b68ee;
}

.rename-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.edit-profile-button {
  padding: 8px 12px;
  background: #3498db;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-profile-button:hover:not(:disabled) {
  background: #2980b9;
}

.edit-profile-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-input:focus {
  outline: none;
  border-color: #4fbdba;
}

.search-input::placeholder {
  color: #666;
}


.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.table th {
  background: #0f3460;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #4fbdba;
  border-bottom: 2px solid #2c3e50;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  padding: 12px;
  border-bottom: 1px solid #2c3e50;
  vertical-align: top;
}

.table tbody tr:hover {
  background: #1a252f;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  white-space: nowrap;
}

.status-pending {
  background: #e74c3c;
  color: white;
}

.status-in_progress {
  background: #f39c12;
  color: white;
}

.status-completed {
  background: #27ae60;
  color: white;
}

.task-number {
  background: #0f4c75;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
}

.task-number.clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.task-number.clickable:hover {
  background: #1e5f8e;
  transform: scale(1.05);
}

.task-number.copied {
  background: #27ae60;
}

.task-id {
  font-size: 0.75rem;
  color: #7f8c8d;
  font-family: monospace;
  word-break: break-all;
}

.task-id-clickable {
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
}

.task-id-clickable:hover {
  color: #3b82f6;
  transform: scale(1.05);
}

.task-id-clickable.copied {
  color: #27ae60;
}

.task-name {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.task-description {
  color: #cccccc;
  font-size: 0.85rem;
  line-height: 1.4;
  max-width: 300px;
}

.task-meta {
  color: #888;
  font-size: 0.8rem;
  margin-top: 4px;
}

.task-id-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.copy-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.8rem;
  padding: 2px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.copy-button:hover {
  opacity: 1;
}

.loading {
  text-align: center;
  padding: 50px;
  color: #888;
  font-size: 1.1rem;
}

.error {
  background: #e74c3c;
  color: white;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #0f3460;
}

.pagination-info {
  color: #aaa;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  gap: 10px;
}

.pagination-controls button {
  padding: 8px 12px;
  background: #16213e;
  border: 1px solid #2c3e50;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pagination-controls button:hover:not(:disabled) {
  background: #2c3e50;
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stats-grid {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  align-items: center;
}

.stat-card {
  background: #16213e;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  border: 1px solid #2c3e50;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.stat-card h3 {
  color: #4fbdba;
  font-size: 0.7rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card .value {
  font-size: 1rem;
  font-weight: bold;
  color: #ffffff;
  margin: 0;
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .profile-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .tabs-list {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .tab {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
  
  .stats-and-search-container {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .controls-right {
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .content-container {
    overflow-x: auto;
  }
  
  .table {
    min-width: 800px;
  }
  
  .stats-grid {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #16213e;
  border-radius: 10px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  border: 2px solid #4fbdba;
}

.modal-content h3 {
  color: #4fbdba;
  margin: 0 0 20px 0;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #fff;
  margin-bottom: 8px;
  font-weight: 600;
}

.form-group input[type="text"],
.form-group input[type="file"] {
  width: 100%;
  padding: 12px;
  background: #0f3460;
  border: 2px solid #2c3e50;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
}

.form-group input[type="text"]:focus,
.form-group input[type="file"]:focus {
  outline: none;
  border-color: #4fbdba;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 30px;
}

.primary-btn,
.secondary-btn,
.add-profile-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background: #4fbdba;
  color: white;
}

.primary-btn:hover {
  background: #3a9b98;
}

.secondary-btn {
  background: #2c3e50;
  color: white;
}

.secondary-btn:hover {
  background: #34495e;
}

.add-profile-btn {
  background: #27ae60;
  color: white;
  white-space: nowrap;
}

.add-profile-btn:hover {
  background: #2ecc71;
}

/* Sortable Table Headers */
.table th.sortable {
  cursor: pointer;
  user-select: none;
}

.table th.sortable:hover {
  background: #1a4480;
}

/* Clickable Table Rows */
.clickable-row {
  cursor: pointer;
  transition: background 0.2s;
}

.clickable-row:hover {
  background: rgba(255, 255, 255, 0.03);
}

/* Task Detail View Styles */
.task-detail-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1f2e;
  border-radius: 8px;
}

.task-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2c3e50;
  background: rgba(0, 0, 0, 0.2);
}

.task-detail-header h2 {
  margin: 0;
  color: #3498db;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-detail-header .task-number {
  background: #0f4c75;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
}

.back-button {
  background: #2c3e50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.2s;
}

.back-button:hover {
  background: #34495e;
}

.task-detail-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.task-detail-footer {
  padding: 20px;
  border-top: 1px solid #2c3e50;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
}

.task-detail-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #2c3e50;
}

.task-detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.task-detail-section h3 {
  color: #3498db;
  margin: 0 0 12px 0;
  font-size: 18px;
}

.detail-row {
  display: flex;
  gap: 12px;
  margin-bottom: 10px;
  align-items: center;
}

.detail-label {
  font-weight: 600;
  color: #95a5a6;
  min-width: 100px;
}

.detail-value {
  color: #ecf0f1;
}

.detail-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 6px;
  color: #ecf0f1;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.monospace {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
}

.dependency-list {
  margin: 0;
  padding-left: 20px;
  list-style-type: none;
}

.dependency-list li {
  margin-bottom: 6px;
  color: #95a5a6;
}

.dependency-list li::before {
  content: "→ ";
  color: #3498db;
  font-weight: bold;
}

.related-files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-file-item {
  display: flex;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 6px;
  align-items: flex-start;
}

.file-type-icon {
  font-size: 20px;
  min-width: 30px;
  text-align: center;
}

.file-info {
  flex: 1;
}

.file-path {
  color: #3498db;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-link {
  color: #3b82f6;
  text-decoration: none;
  cursor: pointer;
}

.file-link:hover {
  text-decoration: underline;
  color: #60a5fa;
}

.file-link.copied {
  color: #27ae60;
}

.dependency-link {
  color: #3b82f6;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.85rem;
}

.dependency-link:hover {
  text-decoration: underline;
  color: #60a5fa;
}

.dependencies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.dependency-badge {
  cursor: pointer;
  transition: all 0.2s;
}

.dependency-badge:hover {
  background: #1e5f8e;
  transform: scale(1.05);
}

.form-hint {
  display: block;
  color: #888;
  font-size: 0.85rem;
  margin-top: 5px;
}

.file-description {
  color: #95a5a6;
  font-size: 14px;
  margin-top: 4px;
}

.file-lines {
  color: #7f8c8d;
  font-size: 12px;
  margin-top: 4px;
}

/* Release Notes Styles */
.release-notes-tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.release-notes-inner {
  background: #16213e;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.release-notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 2px solid #2c3e50;
  background: #16213e;
}

.release-notes-header h2 {
  margin: 0;
  color: #4fbdba;
  font-size: 1.5rem;
}

.release-notes-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.release-sidebar {
  width: 250px;
  background: #16213e;
  border-right: 2px solid #2c3e50;
  overflow-y: auto;
  padding: 20px;
}

.release-sidebar h3 {
  color: #4fbdba;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.version-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.version-list li {
  margin-bottom: 5px;
}

.version-button {
  width: 100%;
  background: #2c3e50;
  border: none;
  border-radius: 6px;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.version-button:hover {
  background: #34495e;
}

.version-button.active {
  background: #4fbdba;
  color: #16213e;
}

.version-number {
  font-weight: bold;
  font-size: 1rem;
}

.version-date {
  font-size: 0.85rem;
  opacity: 0.8;
}

.version-title {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 3px;
  display: block;
  line-height: 1.3;
}

.release-details {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.release-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #2c3e50;
}

.release-header h2 {
  margin: 0;
  color: #ffffff;
}

.release-date {
  color: #95a5a6;
  font-size: 1rem;
}

.release-markdown-content {
  line-height: 1.7;
}

.release-h1 {
  color: #4fbdba;
  font-size: 2.2rem;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.release-h2 {
  color: #3498db;
  font-size: 1.8rem;
  font-weight: bold;
  margin-top: 35px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #2c3e50;
}

.release-h3 {
  color: #ff8c00;
  font-size: 1.4rem;
  font-weight: bold;
  margin-top: 25px;
  margin-bottom: 12px;
}

.release-h4 {
  color: #f39c12;
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
}

.release-text {
  color: #ecf0f1;
  margin-bottom: 12px;
  font-size: 1rem;
}

.release-text.italic {
  font-style: italic;
  color: #95a5a6;
  font-size: 1.1rem;
  margin: 15px 0;
}

.release-list-item {
  color: #ecf0f1;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  font-size: 0.95rem;
}

.release-list-item.nested {
  padding-left: 40px;
  font-size: 0.9rem;
  color: #bdc3c7;
}

.release-list-item strong {
  color: #3498db;
  font-weight: 600;
}

.inline-code {
  background: rgba(52, 73, 94, 0.6);
  color: #4fbdba;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(79, 189, 186, 0.3);
}

.release-spacer {
  height: 10px;
}

.release-divider {
  border: none;
  border-top: 1px solid #34495e;
  margin: 25px 0;
}

.release-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
  font-size: 1.1rem;
}

.close-button {
  background: none;
  border: none;
  color: #95a5a6;
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ffffff;
}

/* Code block styling */
.code-block-wrapper {
  position: relative;
  margin: 1rem 0;
}

/* Override react-syntax-highlighter styles for black background */
.code-block-wrapper pre {
  background: #000000 !important;
  border: 1px solid #444444 !important;
  border-radius: 6px !important;
  margin: 0 !important;
}

/* Copy button styling */
.code-copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #2c3e50;
  color: #ffffff;
  border: 1px solid #444444;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 10;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.code-copy-button:hover {
  background: #34495e;
  border-color: #555555;
}

.code-copy-button.copied {
  background: #27ae60;
  border-color: #27ae60;
}

.code-copy-button:active {
  transform: scale(0.95);
}