{
  "augment.advanced": {
        "mcpServers": [
            {
                "name": "feedback-enhanced",
                "command": "uvx",
                "args": [
                    "mcp-feedback-enhanced@latest"
                ],
                "env": {
                    "MCP_DEBUG": "false",
                    "MCP_WEB_HOST": "127.0.0.1",
                    "MCP_WEB_PORT": "8768",
                    "MCP_LANGUAGE": "en"
                },
                "timeout": 9999,
                "autoApprove": ["interactive_feedback"]
            },
        ]
    }
}
