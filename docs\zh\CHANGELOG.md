[English](../../CHANGELOG.md) | [中文](CHANGELOG.md)

# 更新日誌

## [1.0.21] - 2025-01-13

### 新增

- **任務查看器工具**：完整的基於 React 的任務管理網頁界面
  - 現代化的標籤介面，支援拖放式標籤重新排序
  - 即時搜尋和過濾所有任務字段
  - 可配置的自動刷新間隔（5秒到5分鐘）
  - 針對開發優化的專業深色主題
  - 適應所有螢幕尺寸的響應式設計
  - 使用 TanStack React Table 實現排序和分頁
  - 帶有即時計數的任務統計儀表板
  - 支援添加/刪除的配置文件管理
  - 開發時的熱模塊替換
  - 包含截圖的完整文檔
- 新增對 ListRoots 協議的支援，優化 DATA_DIR 配置方式 (99baa0f)
- 新增 WEB_PORT 環境變數以自訂 WebGUI 埠號 (8771a5b)

### 增強

- **主要 README**：新增任務查看器部分，包含功能亮點和連結
- **文檔**：任務查看器的完整設置指南和使用說明
- **視覺資源**：展示新介面的專業截圖

### 技術

- **React 19 + Vite**：具有熱重載的現代開發環境
- **Node.js HTTP 伺服器**：用於任務和配置文件管理的 RESTful API 端點
- **HTML5 拖放**：直觀的標籤重新排序原生瀏覽器 API
- **CSS Grid 和 Flexbox**：以移動優先方法的響應式佈局系統

### 變更

- 更新相關文檔說明以反映新的配置選項 (99baa0f, 8771a5b)

### 修復

- 修復 #56：新增可配置的 WebGUI 埠號以避免埠號衝突 (8771a5b)

## [1.0.20]

### 新增

- 新增重置按鈕和縮略圖
- 增強關係依賴圖和任務列表交互，篩選和點擊任務列表使關係依賴圖響應變化

### 變更

- 移除關係依賴圖開頭動畫，避免動畫跳變
- 優化關係依賴圖初始狀態

## [1.0.19]

### 新增

- 新增研究模式功能，用於系統性程式編程研究 (5267fa4)
- 新增研究模式的英文和中文提示詞模板 (5267fa4)
- 新增完整的研究模式文檔和使用指南 (288bec9)

### 變更

- 增強 README，新增研究模式功能描述和使用說明 (288bec9)
- 更新中文文檔，包含研究模式功能說明 (288bec9)

## [1.0.18]

### 修復

- 修復 #29：移除不必要的 console.log 輸出以減少雜訊 (7cf1a18)
- 修復 #28：修正 WebGUI 任務詳情檢視的國際化問題 (fd26bfa)

### 變更

- 增強 WebGUI 任務詳情檢視，為所有標籤使用適當的翻譯函數 (fd26bfa)
- 更新思維過程階段描述為英文，提升一致性 (fd26bfa)

## [1.0.17]

### 修復

- 修復 #26：修正 WebGUI 中任務狀態顯示為中文的問題 (16913ad)
- 修復 #26：優化 WebGUI 預設語系會根據環境變數 TEMPLATES_USE 設定改變 (51436bb)

### 變更

- 更新 .env.example 以包含語系設定說明文檔 (51436bb)
- 增強 WebGUI 語言處理邏輯，提供更好的國際化支援 (51436bb)

## [1.0.16]

### 修復

- 修復：修正 Augment AI 不支援 uuid format 問題，改用自定義正則表達式進行驗證 (4264fa7)

### 變更

- 更新任務規劃相關提示詞，新增嚴重警告以禁止假設、猜測與幻想，強調必須使用可用工具收集真實資訊 (cb838cb)
- 調整任務描述以更清楚表達任務目標 (cb838cb)
- 優化錯誤訊息提示，增加分批呼叫建議以解決長文本格式問題 (cb838cb)

## [1.0.15]

### 修復

- 修復：修正 gemini-2.5-pro-preview-05-06 會跳過執行任務直接完成的錯誤 (6d8a422)
- 修復問題 #20 (5d1c28d)

### 變更

- 將 rule.md 改為存放至根目錄，這樣 DATA_DIR 才可以放置在專案外為後續協作架構做準備 (313e338)
- 更新文檔 (28984f)

## [1.0.14]

### 變更

- 優化提示詞以減少 token 使用量並改進指導內容。 (662b3be, 7842e0d)
- 更新英文提示詞，提升清晰度與效率。 (7842e0d)
- 重構工具架構，提高組織性與可維護性。 (04f55cb)
- 優化工作流程，減少不必要步驟。 (3037d4e)

### 移除

- 移除未使用的程式碼與檔案。 (ea40e78)

## [1.0.13]

### 修復

- 修復：修正 invariantlabs 誤判問題 (148f0cd)

## [1.0.12]

### 新增

- 新增蝦米任務管理器示範影片連結至 README 和中文 README，並新增示範影片圖片檔案。 (406eb46)
- 新增 JSON 格式注意事項，強調禁止註解及特殊字元轉義要求，以避免解析失敗。 (a328322)
- 新增網頁圖形介面功能，透過環境變數 `ENABLE_GUI` 控制。 (bf5f367)

### 移除

- 移除多處不必要的錯誤日誌輸出，避免 Cursor 報錯。 (552eed8)

## [1.0.11]

### 變更

- 移除不再使用的函數。 (f8d9c8)

### 修復

- 修復：修正 Cursor Console 不支援中文的問題。 (00943e1)

## [1.0.10]

### 變更

- 新增專案規則更新模式指南，強調遞迴檢查所有資料夾與檔案，並規範模糊請求的自主處理流程。 (989af20)
- 新增提示詞語言與客製化說明，更新 README 和中文文檔。 (d0c3bfa)
- 新增 `TEMPLATES_USE` 配置選項以支持自定義提示詞模板，更新 README 和中文文檔。 (76315fe)
- 新增多語系支持的任務模板 (英/中)。 (291c5ee)
- 新增多個任務相關的 prompt 生成器及模板 (刪除、清除、更新任務)。 (a51110f, 52994d5, 304e0cd)
- 調整任務模板為 Markdown 格式，方便多語系支持與修改。 (5513235)
- 調整 `split_tasks` 工具的「分批提交」參數限制，從 8000 字減少至 5000 字。 (a395686)
- 移除不再使用的更新任務相關文件工具。 (fc1a5c8)
- 更新 README 和中文文檔：新增「推薦模型」、連結 MIT 許可協議、新增 Star History、新增目錄與標籤、強化使用指南。 (5c61b3e, f0283ff, 0bad188, 31065fa)
- 更新任務內容描述：允許已完成任務更新相關文件摘要，調整思考流程描述。 (b07672c)
- 更新任務模板：新增「請嚴格遵守以下指導」提示，強化指導性。 (f0283ff)

### 修復

- 修正部分模型可能不遵守流程的問題。 (ffd6349)
- 修復 #6：修正簡繁中文導致 Enum 判斷參數錯誤問題。 (dae3756)

## [1.0.8]

### 新增

- 新增 zod-to-json-schema 依賴項，用於更好的結構定義整合
- 新增詳細的任務拆分指南，改善任務管理體驗
- 新增更強大的 Agent 工具呼叫錯誤處理機制

### 變更

- 更新 MCP SDK 整合方式，提升錯誤處理和相容性
- 改進任務實現提示模板，使指令更加清晰
- 優化任務拆分工具描述和參數驗證

### 修復

- 修復問題 #5，修正部分 Agent 無法正常處理錯誤的問題
- 修復模板文件中的行格式，提高可讀性

## [1.0.7]

### 新增

- 新增思維鏈過程功能，用於系統性問題分析
- 新增專案規範初始化功能，用於維護專案一致性

### 變更

- 更新相關文檔，強調系統性問題分析與專案一致性維護
- 調整工具列表，包含新功能
- 更新 .gitignore，排除不必要的資料夾
